import tushare as ts

# 设置token（注册后免费获取）
ts.set_token('023f8c0b55895bd6150f6410719f1fbb29e65140ef689f581999235d')
pro = ts.pro_api()


def generate_ai_command(stock_code):
    # 1.获取实时数据
    quote = pro.daily(ts_code=stock_code + '.SH')  # 深市用.SZ
    latest = quote.iloc[0]  # 最新交易日数据

    # 2.获取MACD/RSI（需计算，此处简化为模拟值）
    tech_data = {
        'macd': '金叉',
        'rsi': 65,
        'boll_mid': 185.0
    }

    # 3.生成大模型指令
    command = f"""
根据以下{stock_code}实时数据，制定买卖点计划：
- 最新价：{latest['close']}元
- 5日均线：{latest['ma5']:.2f}元（计算值）
- 20日均线：{latest['ma20']:.2f}元（计算值）
- 成交量变化：近5日日均量{latest['vol'] / 10000:.1f}万手
- 技术指标：MACD={tech_data['macd']}, RSI={tech_data['rsi']}, BOLL中轨={tech_data['boll_mid']}元
- 我的策略：短线波段（1-4周），能承受8%回撤，目标收益15%
请输出具体买卖价格与风控规则
    """
    return command


# 示例：生成宁德时代分析指令
print(generate_ai_command('300750'))