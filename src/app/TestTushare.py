import tushare as ts

# 设置token（注册后免费获取）
ts.set_token('023f8c0b55895bd6150f6410719f1fbb29e65140ef689f581999235d')
pro = ts.pro_api()


def generate_ai_command(stock_code):
    # 1.获取实时数据
    # 判断股票代码所属交易所
    if stock_code.startswith('6'):
        ts_code = stock_code + '.SH'  # 上海交易所
    elif stock_code.startswith('0') or stock_code.startswith('3'):
        ts_code = stock_code + '.SZ'  # 深圳交易所
    else:
        ts_code = stock_code + '.SH'  # 默认上海交易所

    quote = pro.daily(ts_code=ts_code, limit=20)  # 获取最近20天数据用于计算均线

    # 检查数据是否为空
    if quote.empty:
        return f"错误：无法获取股票代码 {stock_code} 的数据，请检查代码是否正确"

    latest = quote.iloc[0]  # 最新交易日数据

    # 计算5日和20日均线（简化计算）
    if len(quote) >= 5:
        ma5 = quote['close'].head(5).mean()
    else:
        ma5 = latest['close']

    if len(quote) >= 20:
        ma20 = quote['close'].head(20).mean()
    else:
        ma20 = quote['close'].mean()

    # 2.获取MACD/RSI（需计算，此处简化为模拟值）
    tech_data = {
        'macd': '金叉',
        'rsi': 65,
        'boll_mid': 185.0
    }

    # 3.生成大模型指令
    command = f"""
根据以下{stock_code}实时数据，制定买卖点计划：
- 最新价：{latest['close']}元
- 5日均线：{ma5:.2f}元（计算值）
- 20日均线：{ma20:.2f}元（计算值）
- 成交量变化：近5日日均量{latest['vol'] / 10000:.1f}万手
- 技术指标：MACD={tech_data['macd']}, RSI={tech_data['rsi']}, BOLL中轨={tech_data['boll_mid']}元
- 我的策略：短线波段（1-4周），能承受8%回撤，目标收益15%
请输出具体买卖价格与风控规则
    """
    return command


# 示例：生成宁德时代分析指令
print(generate_ai_command('300750'))